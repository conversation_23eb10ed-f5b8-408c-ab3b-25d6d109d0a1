# Network Setup Guide - Client-Server on Separate Computers

This guide explains how to set up the IC Share client-server architecture across two separate computers on the same network.

## Overview

- **Server Computer**: Runs the server that captures screenshots and webcam images
- **Client Computer**: Runs the client that processes images with AI and displays the UI

## Prerequisites

1. Both computers must be on the same network (WiFi or Ethernet)
2. Port 3001 must be accessible between the computers
3. Firewall settings may need to be adjusted

## Step-by-Step Setup

### 1. Server Computer Setup

#### A. Find the Server Computer's IP Address

**Windows:**
```cmd
ipconfig
```
Look for "IPv4 Address" (e.g., *************)

**macOS:**
```bash
ifconfig | grep "inet " | grep -v 127.0.0.1
```

**Linux:**
```bash
ip addr show | grep "inet " | grep -v 127.0.0.1
```

#### B. Configure Firewall (if needed)

**Windows:**
1. Open Windows Defender Firewall
2. Click "Allow an app or feature through Windows Defender Firewall"
3. Click "Change Settings" → "Allow another app"
4. Browse to the server executable or add port 3001

**macOS:**
1. System Preferences → Security & Privacy → Firewall
2. Click "Firewall Options"
3. Add the server application or allow incoming connections

**Linux (Ubuntu):**
```bash
sudo ufw allow 3001
```

#### C. Start the Server

**Windows:**
```cmd
server.bat
```

**macOS/Linux:**
```bash
./server.sh
```

The server will display:
```
Server running on 0.0.0.0:3001
HTTP API available at: http://0.0.0.0:3001
WebSocket server available at: ws://0.0.0.0:3001
```

### 2. Client Computer Setup

#### A. Configure Server Connection

**Method 1: Environment Variable**

**Windows:**
```cmd
set SERVER_URL=http://*************:3001
client.bat
```

**macOS/Linux:**
```bash
SERVER_URL=http://*************:3001 ./client.sh
```

**Method 2: Client Configuration (Persistent)**

1. Start the client normally first time
2. Open client settings
3. Set server URL to: `*************`
4. Set server port to: `3001`
5. Restart the client

#### B. Start the Client

**Windows:**
```cmd
client.bat
```

**macOS/Linux:**
```bash
./client.sh
```

## Testing the Connection

### 1. Test HTTP Connection

From the client computer, open a web browser and navigate to:
```
http://[SERVER-IP]:3001/health
```

You should see:
```json
{"status":"ok","timestamp":"2024-01-01T12:00:00.000Z"}
```

### 2. Test from Command Line

**Windows (PowerShell):**
```powershell
Invoke-RestMethod -Uri "http://*************:3001/health"
```

**macOS/Linux:**
```bash
curl http://*************:3001/health
```

### 3. Check Client Connection

In the client application logs, you should see:
```
Connected to server
```

## Troubleshooting

### Connection Refused Errors

**Problem:** Client shows "ECONNREFUSED" errors

**Solutions:**
1. Verify server is running and listening on 0.0.0.0:3001
2. Check firewall settings on server computer
3. Verify IP address is correct
4. Test with `ping [SERVER-IP]` from client computer

### Firewall Issues

**Problem:** Connection times out

**Solutions:**
1. Temporarily disable firewall to test
2. Add specific firewall rules for port 3001
3. Check router/network firewall settings

### Network Discovery

**Problem:** Can't find server IP address

**Solutions:**
1. Use network scanner: `nmap -sn ***********/24`
2. Check router admin panel for connected devices
3. Use `arp -a` to see network devices

### Port Already in Use

**Problem:** Server can't start on port 3001

**Solutions:**
1. Change port: `PORT=8080 ./server.sh`
2. Find what's using port: `netstat -an | grep 3001`
3. Kill conflicting process

## Advanced Configuration

### Custom Ports

**Server:**
```bash
# Windows
set PORT=8080
server.bat

# macOS/Linux
PORT=8080 ./server.sh
```

**Client:**
```bash
# Windows
set SERVER_URL=http://*************:8080
client.bat

# macOS/Linux
SERVER_URL=http://*************:8080 ./client.sh
```

### Multiple Clients

You can connect multiple clients to the same server:

1. Start server on one computer
2. Start client on computer A: `SERVER_URL=http://[SERVER-IP]:3001 ./client.sh`
3. Start client on computer B: `SERVER_URL=http://[SERVER-IP]:3001 ./client.sh`

Both clients will receive the same captured images.

### Security Considerations

**For Local Network Use:**
- The current setup is designed for trusted local networks
- No authentication is required
- Data is transmitted unencrypted

**For Production/Internet Use:**
- Consider adding HTTPS/WSS support
- Implement authentication
- Use VPN for secure connections

## Network Requirements

- **Bandwidth**: Minimal (images are compressed)
- **Latency**: Low latency preferred for real-time updates
- **Ports**: TCP port 3001 (or custom port)
- **Protocol**: HTTP/WebSocket

## Example Network Topology

```
┌─────────────────┐    WiFi/Ethernet    ┌─────────────────┐
│  Server PC      │ ◄─────────────────► │  Client PC      │
│  *************  │                     │  *************  │
│  Port 3001      │                     │                 │
│                 │                     │                 │
│ • Screenshots   │                     │ • AI Processing │
│ • Webcam        │                     │ • User Interface│
│ • HTTP API      │                     │ • Results       │
│ • WebSocket     │                     │                 │
└─────────────────┘                     └─────────────────┘
```

## Success Indicators

✅ **Server Started Successfully:**
- Server shows "Server running on 0.0.0.0:3001"
- No error messages about port binding

✅ **Client Connected Successfully:**
- Client shows "Connected to server"
- No "ECONNREFUSED" errors

✅ **Communication Working:**
- Health endpoint returns JSON response
- Client receives screenshot/webcam capture events
- Images appear in client interface

## Support

If you encounter issues:

1. Check both server and client logs for error messages
2. Verify network connectivity with `ping`
3. Test HTTP endpoint with browser or curl
4. Check firewall and antivirus settings
5. Try different ports if 3001 is blocked
