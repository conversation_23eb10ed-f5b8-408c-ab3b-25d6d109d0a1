// Client ipcHandlers.ts - Handles IPC communication for client
import { ipc<PERSON>ain, <PERSON>rowserWindow } from "electron"
import { <PERSON>ingHelper } from "./ProcessingHelper"
import { ServerConnection } from "./ServerConnection"
import { configHelper } from "./ConfigHelper"

export interface IpcHandlerDeps {
  getMainWindow: () => BrowserWindow | null
  processingHelper: ProcessingHelper | null
  serverConnection: ServerConnection | null
  PROCESSING_EVENTS: {
    INITIAL_START: string
    SOLUTION_SUCCESS: string
    INITIAL_SOLUTION_ERROR: string
    DEBUG_START: string
    DEBUG_SUCCESS: string
    DEBUG_ERROR: string
    API_KEY_INVALID: string
    NO_SCREENSHOTS: string
  }
  getView: () => string
  setView: (view: string) => void
}

export function initializeIpcHandlers(deps: IpcHandlerDeps) {
  console.log("Initializing client IPC handlers...")

  // Configuration handlers
  ipcMain.handle("get-config", () => {
    return configHelper.loadConfig()
  })

  ipcMain.handle("update-config", (_event, updates) => {
    return configHelper.updateConfig(updates)
  })

  ipcMain.handle("check-api-key", () => {
    return configHelper.hasApiKey()
  })

  ipcMain.handle("validate-api-key", async (_event, apiKey) => {
    if (!configHelper.isValidApiKeyFormat(apiKey)) {
      return {
        valid: false,
        error: "Invalid API key format"
      }
    }

    const result = await configHelper.testApiKey(apiKey)
    return result
  })

  // Server connection handlers
  ipcMain.handle("server-health-check", async () => {
    try {
      if (!deps.serverConnection) {
        return { connected: false, error: "Server connection not initialized" }
      }
      
      const isHealthy = await deps.serverConnection.checkServerHealth()
      return { connected: isHealthy }
    } catch (error) {
      console.error("Server health check failed:", error)
      return { connected: false, error: error instanceof Error ? error.message : String(error) }
    }
  })

  ipcMain.handle("server-connection-status", () => {
    if (!deps.serverConnection) {
      return { connected: false }
    }
    return { connected: deps.serverConnection.isConnected() }
  })

  // Screenshot capture handlers
  ipcMain.handle("trigger-screenshot", async () => {
    const mainWindow = deps.getMainWindow()
    if (mainWindow && deps.processingHelper) {
      try {
        await deps.processingHelper.triggerScreenshotCapture()
        return { success: true }
      } catch (error) {
        console.error("Error triggering screenshot:", error)
        return { error: "Failed to trigger screenshot" }
      }
    }
    return { error: "No main window or processing helper available" }
  })

  // Webcam capture handlers
  ipcMain.handle("capture-webcam-image", async () => {
    try {
      if (!deps.processingHelper) {
        return { success: false, error: "Processing helper not initialized" }
      }
      
      await deps.processingHelper.triggerWebcamCapture()
      return { success: true }
    } catch (error) {
      console.error("Error capturing webcam image:", error)
      return { success: false, error: "Failed to capture webcam image" }
    }
  })

  ipcMain.handle("get-available-webcams", async () => {
    try {
      if (!deps.serverConnection) {
        return { success: false, error: "Server connection not available" }
      }
      
      const devices = await deps.serverConnection.getWebcamDevices()
      return { success: true, devices }
    } catch (error) {
      console.error("Error getting webcam devices:", error)
      return { success: false, error: "Failed to get webcam devices" }
    }
  })

  ipcMain.handle("set-webcam-device", async (_event, deviceId) => {
    try {
      if (!deps.serverConnection) {
        return { success: false, error: "Server connection not available" }
      }
      
      const success = await deps.serverConnection.setWebcamDevice(deviceId)
      return { success }
    } catch (error) {
      console.error("Error setting webcam device:", error)
      return { success: false, error: "Failed to set webcam device" }
    }
  })

  // Processing handlers
  ipcMain.handle("trigger-process-screenshots", async () => {
    try {
      if (!configHelper.hasApiKey()) {
        const mainWindow = deps.getMainWindow()
        if (mainWindow) {
          mainWindow.webContents.send(deps.PROCESSING_EVENTS.API_KEY_INVALID)
        }
        return { success: false, error: "API key required" }
      }

      await deps.processingHelper?.processScreenshots()
      return { success: true }
    } catch (error) {
      console.error("Error processing screenshots:", error)
      return { error: "Failed to process screenshots" }
    }
  })

  // View management handlers
  ipcMain.handle("get-view", () => {
    return deps.getView()
  })

  ipcMain.handle("set-view", (_event, view) => {
    deps.setView(view)
    return { success: true }
  })

  // Image management handlers
  ipcMain.handle("get-captured-images", () => {
    if (!deps.processingHelper) {
      return []
    }
    return deps.processingHelper.getCapturedImages()
  })

  ipcMain.handle("clear-captured-images", () => {
    if (deps.processingHelper) {
      deps.processingHelper.clearCapturedImages()
    }
    return { success: true }
  })

  // Language handlers
  ipcMain.handle("get-language", () => {
    return configHelper.getLanguage()
  })

  ipcMain.handle("set-language", (_event, language) => {
    configHelper.setLanguage(language)
    return { success: true }
  })

  // Opacity handlers
  ipcMain.handle("get-opacity", () => {
    return configHelper.getOpacity()
  })

  ipcMain.handle("set-opacity", (_event, opacity) => {
    configHelper.setOpacity(opacity)
    return { success: true }
  })

  // Window management handlers
  ipcMain.handle("toggle-window-visibility", () => {
    const mainWindow = deps.getMainWindow()
    if (mainWindow) {
      if (mainWindow.isVisible()) {
        mainWindow.hide()
      } else {
        mainWindow.show()
      }
      return { success: true }
    }
    return { error: "No main window available" }
  })

  ipcMain.handle("show-window", () => {
    const mainWindow = deps.getMainWindow()
    if (mainWindow) {
      mainWindow.show()
      return { success: true }
    }
    return { error: "No main window available" }
  })

  ipcMain.handle("hide-window", () => {
    const mainWindow = deps.getMainWindow()
    if (mainWindow) {
      mainWindow.hide()
      return { success: true }
    }
    return { error: "No main window available" }
  })

  // Settings dialog handler
  ipcMain.handle("show-settings", () => {
    const mainWindow = deps.getMainWindow()
    if (mainWindow) {
      mainWindow.webContents.send("show-settings")
      return { success: true }
    }
    return { error: "No main window available" }
  })

  console.log("Client IPC handlers initialized successfully")
}
