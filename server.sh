#!/bin/bash
echo "=== Interview Coder Server ==="
echo
echo "Starting the Interview Coder Server..."
echo "This server will handle screenshot and webcam capture."
echo
echo "Server will be available at: http://localhost:3001"
echo "WebSocket server will be available at: ws://localhost:3001"
echo

# Navigate to script directory
cd "$(dirname "$0")"

echo "=== Step 1: Creating required directories... ==="
mkdir -p ~/.interview-coder-server/temp
mkdir -p ~/.interview-coder-server/screenshots
mkdir -p ~/.interview-coder-server/webcam

echo "=== Step 2: Installing dependencies... ==="
echo "Checking if dependencies are installed..."
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
else
    echo "Dependencies already installed."
fi

echo "=== Step 3: Building server... ==="
echo "Building server application..."
npm run build-server

if [ $? -ne 0 ]; then
    echo
    echo "ERROR: Failed to build server!"
    echo "Please check the error messages above."
    exit 1
fi

echo "=== Step 4: Starting server... ==="
echo "Server is starting..."
echo
echo "To stop the server, press Ctrl+C"
echo

export NODE_ENV=production
npm run run-server

echo
echo "Server has stopped."
