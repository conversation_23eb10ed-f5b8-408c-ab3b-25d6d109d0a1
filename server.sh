#!/bin/bash
echo "=== IC Share Server ==="
echo
echo "Starting the IC Share Server..."
echo "This server will handle screenshot and webcam capture."
echo
echo "NETWORK CONFIGURATION:"
echo "- Server will listen on ALL network interfaces (0.0.0.0:3001)"
echo "- Local access: http://localhost:3001"
echo "- Network access: http://[YOUR-IP]:3001"
echo "- WebSocket: ws://[YOUR-IP]:3001"
echo
echo "To find your IP address, run: ifconfig (macOS/Linux) or ip addr (Linux)"
echo "To use a different port, set PORT environment variable"
echo "Example: PORT=8080 ./server.sh"
echo

# Navigate to script directory
cd "$(dirname "$0")"

echo "=== Step 1: Creating required directories... ==="
mkdir -p ~/.ic-share-server/temp
mkdir -p ~/.ic-share-server/screenshots
mkdir -p ~/.ic-share-server/webcam

echo "=== Step 2: Installing dependencies... ==="
echo "Checking if dependencies are installed..."
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
else
    echo "Dependencies already installed."
fi

echo "=== Step 3: Building server... ==="
echo "Building server application..."
npm run build-server

if [ $? -ne 0 ]; then
    echo
    echo "ERROR: Failed to build server!"
    echo "Please check the error messages above."
    exit 1
fi

echo "=== Step 4: Starting server... ==="
echo "Server is starting..."
echo
echo "To stop the server, press Ctrl+C"
echo

export NODE_ENV=production
npm run run-server

echo
echo "Server has stopped."
