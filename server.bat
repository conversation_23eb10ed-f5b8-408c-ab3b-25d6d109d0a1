@echo off
echo === Interview Coder Server ===
echo.
echo Starting the Interview Coder Server...
echo This server will handle screenshot and webcam capture.
echo.
echo NETWORK CONFIGURATION:
echo - Server will listen on ALL network interfaces (0.0.0.0:3001)
echo - Local access: http://localhost:3001
echo - Network access: http://[YOUR-IP]:3001
echo - WebSocket: ws://[YOUR-IP]:3001
echo.
echo To find your IP address, run: ipconfig
echo To use a different port, set PORT environment variable
echo Example: set PORT=8080 ^&^& server.bat
echo.

cd /D "%~dp0"

echo === Step 1: Creating required directories... ===
mkdir "%USERPROFILE%\.interview-coder-server\temp" 2>nul
mkdir "%USERPROFILE%\.interview-coder-server\screenshots" 2>nul
mkdir "%USERPROFILE%\.interview-coder-server\webcam" 2>nul

echo === Step 2: Installing dependencies... ===
echo Checking if dependencies are installed...
if not exist "node_modules" (
    echo Installing dependencies...
    call npm install
) else (
    echo Dependencies already installed.
)

echo === Step 3: Building server... ===
echo Building server application...
call npm run build-server

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERROR: Failed to build server!
    echo Please check the error messages above.
    pause
    exit /b 1
)

echo === Step 4: Starting server... ===
echo Server is starting...
echo.
echo To stop the server, press Ctrl+C
echo.

set NODE_ENV=production
call npm run run-server

echo.
echo Server has stopped.
pause
