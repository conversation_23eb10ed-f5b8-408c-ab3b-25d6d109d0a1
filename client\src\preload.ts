// Client preload.ts - Exposes APIs to renderer process
import { contextBridge, ipc<PERSON>ender<PERSON> } from "electron"

// Define the API interface
const electronAPI = {
  // Configuration methods
  getConfig: () => ipcRenderer.invoke("get-config"),
  updateConfig: (updates: any) => ipcRenderer.invoke("update-config", updates),
  checkApiKey: () => ipcRenderer.invoke("check-api-key"),
  validateApiKey: (apiKey: string) => ipcRenderer.invoke("validate-api-key", apiKey),

  // Server connection methods
  serverHealthCheck: () => ipcRenderer.invoke("server-health-check"),
  serverConnectionStatus: () => ipcRenderer.invoke("server-connection-status"),

  // Screenshot methods
  triggerScreenshot: () => ipcRenderer.invoke("trigger-screenshot"),
  
  // Webcam methods
  getAvailableWebcams: () => ipcRenderer.invoke("get-available-webcams"),
  setWebcamDevice: (deviceId: string) => ipcRenderer.invoke("set-webcam-device", deviceId),
  captureWebcamImage: () => ipcRenderer.invoke("capture-webcam-image"),

  // Processing methods
  triggerProcessScreenshots: () => ipcRenderer.invoke("trigger-process-screenshots"),

  // View management methods
  getView: () => ipcRenderer.invoke("get-view"),
  setView: (view: string) => ipcRenderer.invoke("set-view", view),

  // Image management methods
  getCapturedImages: () => ipcRenderer.invoke("get-captured-images"),
  clearCapturedImages: () => ipcRenderer.invoke("clear-captured-images"),

  // Language methods
  getLanguage: () => ipcRenderer.invoke("get-language"),
  setLanguage: (language: string) => ipcRenderer.invoke("set-language", language),

  // Opacity methods
  getOpacity: () => ipcRenderer.invoke("get-opacity"),
  setOpacity: (opacity: number) => ipcRenderer.invoke("set-opacity", opacity),

  // Window management methods
  toggleWindowVisibility: () => ipcRenderer.invoke("toggle-window-visibility"),
  showWindow: () => ipcRenderer.invoke("show-window"),
  hideWindow: () => ipcRenderer.invoke("hide-window"),

  // Settings methods
  showSettings: () => ipcRenderer.invoke("show-settings"),

  // Event listeners
  onScreenshotTaken: (callback: (data: any) => void) => {
    const listener = (_event: any, data: any) => callback(data)
    ipcRenderer.on("screenshot-taken", listener)
    return () => ipcRenderer.removeListener("screenshot-taken", listener)
  },

  onProcessingStatus: (callback: (data: any) => void) => {
    const listener = (_event: any, data: any) => callback(data)
    ipcRenderer.on("processing-status", listener)
    return () => ipcRenderer.removeListener("processing-status", listener)
  },

  onShowSettings: (callback: () => void) => {
    const listener = () => callback()
    ipcRenderer.on("show-settings", listener)
    return () => ipcRenderer.removeListener("show-settings", listener)
  },

  onApiKeyInvalid: (callback: () => void) => {
    const listener = () => callback()
    ipcRenderer.on("API_KEY_INVALID", listener)
    return () => ipcRenderer.removeListener("API_KEY_INVALID", listener)
  },

  onSolutionSuccess: (callback: (data: any) => void) => {
    const listener = (_event: any, data: any) => callback(data)
    ipcRenderer.on("SOLUTION_SUCCESS", listener)
    return () => ipcRenderer.removeListener("SOLUTION_SUCCESS", listener)
  },

  onInitialStart: (callback: () => void) => {
    const listener = () => callback()
    ipcRenderer.on("INITIAL_START", listener)
    return () => ipcRenderer.removeListener("INITIAL_START", listener)
  },

  onInitialSolutionError: (callback: (error: string) => void) => {
    const listener = (_event: any, error: string) => callback(error)
    ipcRenderer.on("INITIAL_SOLUTION_ERROR", listener)
    return () => ipcRenderer.removeListener("INITIAL_SOLUTION_ERROR", listener)
  },

  onDebugStart: (callback: () => void) => {
    const listener = () => callback()
    ipcRenderer.on("DEBUG_START", listener)
    return () => ipcRenderer.removeListener("DEBUG_START", listener)
  },

  onDebugSuccess: (callback: (data: any) => void) => {
    const listener = (_event: any, data: any) => callback(data)
    ipcRenderer.on("DEBUG_SUCCESS", listener)
    return () => ipcRenderer.removeListener("DEBUG_SUCCESS", listener)
  },

  onDebugError: (callback: (error: string) => void) => {
    const listener = (_event: any, error: string) => callback(error)
    ipcRenderer.on("DEBUG_ERROR", listener)
    return () => ipcRenderer.removeListener("DEBUG_ERROR", listener)
  },

  onNoScreenshots: (callback: () => void) => {
    const listener = () => callback()
    ipcRenderer.on("NO_SCREENSHOTS", listener)
    return () => ipcRenderer.removeListener("NO_SCREENSHOTS", listener)
  },

  // Utility methods
  removeListener: (channel: string, listener: any) => {
    ipcRenderer.removeListener(channel, listener)
  }
}

// Before exposing the API
console.log(
  "About to expose client electronAPI with methods:",
  Object.keys(electronAPI)
)

// Expose the API to the renderer process
contextBridge.exposeInMainWorld("electronAPI", electronAPI)

// Also expose it as a global for compatibility
contextBridge.exposeInMainWorld("__electronAPI", electronAPI)

console.log("Client preload script loaded successfully")
