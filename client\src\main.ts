import { app, BrowserWindow, ipcMain } from "electron"
import path from "path"
import { initializeIpcHandlers } from "./ipcHandlers"
import { ProcessingHelper } from "./ProcessingHelper"
import { ServerConnection } from "./ServerConnection"
import { initAutoUpdater } from "./autoUpdater"
import { configHelper } from "./ConfigHelper"
import * as dotenv from "dotenv"

// Constants
const isDev = process.env.NODE_ENV === "development"

// Load environment variables
dotenv.config()

// Application state
interface AppState {
  mainWindow: BrowserWindow | null
  isWindowVisible: boolean
  windowSize: { width: number; height: number } | null
  screenWidth: number
  screenHeight: number
  step: number
  view: string
  processingHelper: ProcessingHelper | null
  serverConnection: ServerConnection | null
  problemInfo: any
  hasDebugged: boolean
  PROCESSING_EVENTS: {
    INITIAL_START: string
    SOLUTION_SUCCESS: string
    INITIAL_SOLUTION_ERROR: string
    DEBUG_START: string
    DEBUG_SUCCESS: string
    DEBUG_ERROR: string
    API_KEY_INVALID: string
    NO_SCREENSHOTS: string
  }
}

const state: AppState = {
  mainWindow: null,
  isWindowVisible: false,
  windowSize: null,
  screenWidth: 0,
  screenHeight: 0,
  step: 50,
  view: "queue",
  processingHelper: null,
  serverConnection: null,
  problemInfo: null,
  hasDebugged: false,
  PROCESSING_EVENTS: {
    INITIAL_START: "INITIAL_START",
    SOLUTION_SUCCESS: "SOLUTION_SUCCESS", 
    INITIAL_SOLUTION_ERROR: "INITIAL_SOLUTION_ERROR",
    DEBUG_START: "DEBUG_START",
    DEBUG_SUCCESS: "DEBUG_SUCCESS",
    DEBUG_ERROR: "DEBUG_ERROR",
    API_KEY_INVALID: "API_KEY_INVALID",
    NO_SCREENSHOTS: "NO_SCREENSHOTS"
  }
}

// Create main window
function createWindow(): BrowserWindow {
  const mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, "preload.js"),
      webSecurity: false
    },
    show: false,
    frame: true,
    resizable: true,
    minimizable: true,
    maximizable: true,
    closable: true,
    alwaysOnTop: false,
    skipTaskbar: false,
    title: "Interview Coder Client"
  })

  // Load the app
  if (isDev) {
    mainWindow.loadURL("http://localhost:54321")
    mainWindow.webContents.openDevTools()
  } else {
    // Load from the main dist directory (not client/dist)
    mainWindow.loadFile(path.join(__dirname, "../../dist/index.html"))
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()
    state.isWindowVisible = true
  })

  return mainWindow
}

// Initialize helpers
function initializeHelpers() {
  // Initialize server connection
  state.serverConnection = new ServerConnection()
  
  // Initialize processing helper
  state.processingHelper = new ProcessingHelper({
    getMainWindow,
    getView,
    setView,
    getProblemInfo,
    setProblemInfo,
    setHasDebugged,
    getHasDebugged,
    PROCESSING_EVENTS: state.PROCESSING_EVENTS,
    serverConnection: state.serverConnection
  })
}

// Initialize the application
async function initializeApp() {
  try {
    console.log("Initializing Interview Coder Client...")
    
    // Create main window
    state.mainWindow = createWindow()
    
    // Initialize helpers
    initializeHelpers()
    
    // Initialize IPC handlers
    initializeIpcHandlers({
      getMainWindow,
      processingHelper: state.processingHelper,
      serverConnection: state.serverConnection,
      PROCESSING_EVENTS: state.PROCESSING_EVENTS,
      getView,
      setView
    })
    
    // Initialize auto updater in production
    if (!isDev) {
      initAutoUpdater()
    }
    
    console.log("Client initialized successfully")
  } catch (error) {
    console.error("Failed to initialize client:", error)
  }
}

// Getter functions
function getMainWindow(): BrowserWindow | null {
  return state.mainWindow
}

function getView(): string {
  return state.view
}

function setView(view: string): void {
  state.view = view
}

function getProblemInfo(): any {
  return state.problemInfo
}

function setProblemInfo(info: any): void {
  state.problemInfo = info
}

function setHasDebugged(value: boolean): void {
  state.hasDebugged = value
}

function getHasDebugged(): boolean {
  return state.hasDebugged
}

// App event handlers
app.whenReady().then(initializeApp)

app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit()
  }
})

app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// Export state and functions for other modules
export {
  state,
  getMainWindow,
  getView,
  setView,
  getProblemInfo,
  setProblemInfo,
  setHasDebugged,
  getHasDebugged
}
