#!/bin/bash
echo "=== IC Share Client ==="
echo
echo "Starting the IC Share Client..."
echo "This client will connect to the server and handle AI processing."
echo
echo "IMPORTANT: Make sure the server is running first!"
echo "You can start the server by running ./server.sh"
echo
echo "NETWORK CONFIGURATION:"
echo "- Default server: http://localhost:3001"
echo "- To connect to remote server, set SERVER_URL environment variable"
echo "- Example: SERVER_URL=http://*************:3001 ./client.sh"
echo "- You can also configure the server in the client settings"
echo

# Navigate to script directory
cd "$(dirname "$0")"

echo "=== Step 1: Creating required directories... ==="
mkdir -p ~/Library/Application\ Support/ic-share-client/temp
mkdir -p ~/Library/Application\ Support/ic-share-client/cache

echo "=== Step 2: Installing dependencies... ==="
echo "Checking if dependencies are installed..."
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
else
    echo "Dependencies already installed."
fi

echo "=== Step 3: Building client... ==="
echo "Building client application..."
npm run build-client

if [ $? -ne 0 ]; then
    echo
    echo "ERROR: Failed to build client!"
    echo "Please check the error messages above."
    exit 1
fi

echo "=== Step 4: Starting client... ==="
echo "Client is starting..."
echo
echo "The client will attempt to connect to the server at http://localhost:3001"
echo "If the connection fails, make sure the server is running."
echo

export NODE_ENV=production
npm run run-client

echo
echo "Client has stopped."
