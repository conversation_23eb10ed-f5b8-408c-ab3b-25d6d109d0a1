# Interview Coder - Client-Server Architecture

This application has been converted to a client-server architecture where:

- **Server**: Handles screenshot and webcam capture
- **Client**: Handles AI processing and user interface

## Quick Start

### Local Setup (Same Computer)

#### 1. Start the Server

**Windows:**
```bash
server.bat
```

**macOS/Linux:**
```bash
./server.sh
```

#### 2. Start the Client

**Windows:**
```bash
client.bat
```

**macOS/Linux:**
```bash
./client.sh
```

### Network Setup (Separate Computers)

#### 1. Start the Server (on Server Computer)

The server will automatically listen on all network interfaces (0.0.0.0:3001).

**Windows:**
```bash
server.bat
```

**macOS/Linux:**
```bash
./server.sh
```

#### 2. Find Server IP Address

**Windows:**
```cmd
ipconfig
```

**macOS/Linux:**
```bash
ifconfig | grep "inet " | grep -v 127.0.0.1
```

#### 3. Start the Client (on Client Computer)

**Windows:**
```cmd
set SERVER_URL=http://*************:3001
client.bat
```

**macOS/Linux:**
```bash
SERVER_URL=http://*************:3001 ./client.sh
```

Replace `*************` with your server's actual IP address.

📖 **For detailed network setup, see [NETWORK-SETUP-GUIDE.md](NETWORK-SETUP-GUIDE.md)**

## Architecture Overview

```
┌─────────────────┐    WebSocket/HTTP    ┌─────────────────┐
│                 │ ◄─────────────────► │                 │
│     SERVER      │                     │     CLIENT      │
│                 │                     │                 │
│ • Screenshot    │                     │ • User Interface│
│ • Webcam        │                     │ • AI Processing │
│ • Image Capture │                     │ • Results       │
│                 │                     │                 │
└─────────────────┘                     └─────────────────┘
```

## Features

### Server Features
- **Screenshot Capture**: Takes screenshots of the desktop
- **Webcam Capture**: Captures images from connected webcams
- **WebSocket Communication**: Real-time communication with clients
- **REST API**: HTTP endpoints for triggering captures
- **Multi-client Support**: Can serve multiple clients simultaneously

### Client Features
- **AI Processing**: Processes captured images using OpenAI, Gemini, or Anthropic APIs
- **User Interface**: React-based UI for interaction
- **Real-time Updates**: Receives captured images instantly via WebSocket
- **Configuration Management**: Manages API keys and settings

## API Endpoints (Server)

### Health Check
- `GET /health` - Check server status

### Screenshot Capture
- `POST /capture/screenshot` - Trigger screenshot capture

### Webcam Capture
- `POST /capture/webcam` - Trigger webcam image capture
- `GET /webcam/devices` - Get available webcam devices
- `POST /webcam/device` - Set active webcam device

## WebSocket Events

### Server → Client
- `screenshot-captured` - New screenshot available
- `webcam-captured` - New webcam image available

## Configuration

### Server Configuration
The server stores captured images in:
- **Windows**: `%USERPROFILE%\.interview-coder-server\`
- **macOS/Linux**: `~/.interview-coder-server/`

### Client Configuration
The client stores configuration in:
- **Windows**: `%APPDATA%\interview-coder-client\`
- **macOS/Linux**: `~/Library/Application Support/interview-coder-client/`

## Development

### Building the Server
```bash
npm run build-server
```

### Building the Client
```bash
npm run build-client
```

### Running in Development Mode
For development, you can still use the original commands:
```bash
npm run dev
```

## Troubleshooting

### Server Won't Start
1. Check if port 3001 is available
2. Ensure Node.js is installed
3. Run `npm install` to install dependencies

### Client Can't Connect to Server
1. Ensure the server is running first
2. Check if the server is accessible at `http://localhost:3001`
3. Check firewall settings

### Screenshot/Webcam Capture Issues
1. On Windows, ensure PowerShell execution policy allows scripts
2. Check if webcam is being used by another application
3. Verify screen capture permissions on macOS

## Migration from Original App

The original single-application mode is still available:
- Use `stealth-run.bat` or `stealth-run.sh` for the original experience
- Use `server.bat`/`server.sh` + `client.bat`/`client.sh` for client-server mode

## Benefits of Client-Server Architecture

1. **Scalability**: Multiple clients can connect to one server
2. **Separation of Concerns**: Capture logic separated from processing logic
3. **Remote Processing**: Client can run on a different machine than the server
4. **Resource Distribution**: Heavy AI processing on client, lightweight capture on server
5. **Flexibility**: Can easily add new clients or modify existing ones

## Security Considerations

- The server runs on localhost by default (not exposed to external networks)
- WebSocket connections are not encrypted (suitable for local use only)
- For production use, consider adding authentication and HTTPS/WSS

## Future Enhancements

- Authentication system for multi-user environments
- HTTPS/WSS support for secure connections
- Configuration for custom server ports
- Load balancing for multiple servers
- Web-based client (browser interface)
