@echo off
echo === Interview Coder Client ===
echo.
echo Starting the Interview Coder Client...
echo This client will connect to the server and handle AI processing.
echo.
echo IMPORTANT: Make sure the server is running first!
echo You can start the server by running server.bat
echo.
echo NETWORK CONFIGURATION:
echo - Default server: http://localhost:3001
echo - To connect to remote server, set SERVER_URL environment variable
echo - Example: set SERVER_URL=http://*************:3001 ^&^& client.bat
echo - You can also configure the server in the client settings
echo.

cd /D "%~dp0"

echo === Step 1: Creating required directories... ===
mkdir "%APPDATA%\interview-coder-client\temp" 2>nul
mkdir "%APPDATA%\interview-coder-client\cache" 2>nul

echo === Step 2: Installing dependencies... ===
echo Checking if dependencies are installed...
if not exist "node_modules" (
    echo Installing dependencies...
    call npm install
) else (
    echo Dependencies already installed.
)

echo === Step 3: Building client... ===
echo Building client application...
call npm run build-client

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERROR: Failed to build client!
    echo Please check the error messages above.
    pause
    exit /b 1
)

echo === Step 4: Starting client... ===
echo Client is starting...
echo.
echo The client will attempt to connect to the server at http://localhost:3001
echo If the connection fails, make sure the server is running.
echo.

set NODE_ENV=production
call npm run run-client

echo.
echo Client has stopped.
pause
