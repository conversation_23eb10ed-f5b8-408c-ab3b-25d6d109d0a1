// Client autoUpdater.ts - Simple auto updater for client
import { autoUpdater } from "electron-updater"
import { app, BrowserWindow, dialog } from "electron"

export function initAutoUpdater() {
  // Only enable auto updater in production
  if (process.env.NODE_ENV !== "production") {
    console.log("Auto updater disabled in development mode")
    return
  }

  console.log("Initializing auto updater for client...")

  // Configure auto updater
  autoUpdater.checkForUpdatesAndNotify()

  autoUpdater.on("checking-for-update", () => {
    console.log("Checking for client updates...")
  })

  autoUpdater.on("update-available", (info) => {
    console.log("Client update available:", info)
  })

  autoUpdater.on("update-not-available", (info) => {
    console.log("Client update not available:", info)
  })

  autoUpdater.on("error", (err) => {
    console.error("Auto updater error:", err)
  })

  autoUpdater.on("download-progress", (progressObj) => {
    let log_message = "Download speed: " + progressObj.bytesPerSecond
    log_message = log_message + " - Downloaded " + progressObj.percent + "%"
    log_message = log_message + " (" + progressObj.transferred + "/" + progressObj.total + ")"
    console.log(log_message)
  })

  autoUpdater.on("update-downloaded", (info) => {
    console.log("Client update downloaded:", info)
    
    // Show dialog to user
    const mainWindow = BrowserWindow.getFocusedWindow()
    if (mainWindow) {
      dialog.showMessageBox(mainWindow, {
        type: "info",
        title: "Update Ready",
        message: "A new version of IC Share Client has been downloaded. Restart the application to apply the update.",
        buttons: ["Restart Now", "Later"],
        defaultId: 0
      }).then((result) => {
        if (result.response === 0) {
          autoUpdater.quitAndInstall()
        }
      })
    }
  })

  // Check for updates every hour
  setInterval(() => {
    autoUpdater.checkForUpdatesAndNotify()
  }, 60 * 60 * 1000)
}
