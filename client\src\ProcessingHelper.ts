// Client ProcessingHelper.ts - Handles AI processing on client side
import { <PERSON><PERSON><PERSON>Window } from "electron"
import { ServerConnection, CapturedImage } from "./ServerConnection"
import { OpenAI } from "openai"
import { configHelper } from "./ConfigHelper"
import Anthropic from '@anthropic-ai/sdk'
import * as axios from "axios"

export interface IProcessingHelperDeps {
  getMainWindow: () => BrowserWindow | null
  getView: () => string
  setView: (view: string) => void
  getProblemInfo: () => any
  setProblemInfo: (info: any) => void
  setHasDebugged: (value: boolean) => void
  getHasDebugged: () => boolean
  PROCESSING_EVENTS: {
    INITIAL_START: string
    SOLUTION_SUCCESS: string
    INITIAL_SOLUTION_ERROR: string
    DEBUG_START: string
    DEBUG_SUCCESS: string
    DEBUG_ERROR: string
    API_KEY_INVALID: string
    NO_SCREENSHOTS: string
  }
  serverConnection: ServerConnection
}

export class ProcessingHelper {
  private deps: IProcessingHelperDeps
  private openaiClient: OpenAI | null = null
  private geminiApiKey: string | null = null
  private anthropicClient: Anthropic | null = null
  private capturedImages: CapturedImage[] = []
  private currentProcessingAbortController: AbortController | null = null

  constructor(deps: IProcessingHelperDeps) {
    this.deps = deps
    
    // Initialize AI client based on config
    this.initializeAIClient()
    
    // Listen for config changes to re-initialize the AI client
    configHelper.on('config-updated', () => {
      this.initializeAIClient()
    })

    // Listen for captured images from server
    this.deps.serverConnection.on('screenshot-captured', (data: CapturedImage) => {
      console.log('Screenshot captured by server:', data)
      this.capturedImages.push(data)
      this.notifyImageCaptured(data)
    })

    this.deps.serverConnection.on('webcam-captured', (data: CapturedImage) => {
      console.log('Webcam image captured by server:', data)
      this.capturedImages.push(data)
      this.notifyImageCaptured(data)
    })
  }

  private notifyImageCaptured(image: CapturedImage): void {
    const mainWindow = this.deps.getMainWindow()
    if (mainWindow) {
      mainWindow.webContents.send("screenshot-taken", {
        path: image.path,
        preview: image.preview
      })
    }
  }

  /**
   * Initialize or reinitialize the AI client with current config
   */
  private initializeAIClient(): void {
    try {
      const config = configHelper.loadConfig()

      if (config.apiProvider === "openai") {
        if (config.apiKey) {
          this.openaiClient = new OpenAI({
            apiKey: config.apiKey,
            timeout: 60000, // 60 second timeout
            maxRetries: 2   // Retry up to 2 times
          })
          this.geminiApiKey = null
          this.anthropicClient = null
          console.log("OpenAI client initialized successfully")
        } else {
          this.openaiClient = null
          this.geminiApiKey = null
          this.anthropicClient = null
          console.warn("No API key available, OpenAI client not initialized")
        }
      } else if (config.apiProvider === "gemini") {
        // Gemini client initialization
        this.openaiClient = null
        this.anthropicClient = null
        if (config.apiKey) {
          this.geminiApiKey = config.apiKey
          console.log("Gemini API key set successfully")
        } else {
          this.openaiClient = null
          this.geminiApiKey = null
          this.anthropicClient = null
          console.warn("No API key available, Gemini client not initialized")
        }
      } else if (config.apiProvider === "anthropic") {
        // Reset other clients
        this.openaiClient = null
        this.geminiApiKey = null
        if (config.apiKey) {
          this.anthropicClient = new Anthropic({
            apiKey: config.apiKey,
            timeout: 60000,
            maxRetries: 2
          })
          console.log("Anthropic client initialized successfully")
        } else {
          this.openaiClient = null
          this.geminiApiKey = null
          this.anthropicClient = null
          console.warn("No API key available, Anthropic client not initialized")
        }
      }
    } catch (error) {
      console.error("Failed to initialize AI client:", error)
      this.openaiClient = null
      this.geminiApiKey = null
      this.anthropicClient = null
    }
  }

  private async waitForInitialization(mainWindow: BrowserWindow): Promise<void> {
    let attempts = 0
    const maxAttempts = 50 // 5 seconds total

    while (attempts < maxAttempts) {
      const isInitialized = await mainWindow.webContents.executeJavaScript(
        "window.__IS_INITIALIZED__"
      )
      if (isInitialized) return
      await new Promise((resolve) => setTimeout(resolve, 100))
      attempts++
    }
    throw new Error("App failed to initialize after 5 seconds")
  }

  private async getLanguage(): Promise<string> {
    try {
      // Get language from config
      const config = configHelper.loadConfig()
      if (config.language) {
        return config.language
      }

      // Fallback to window variable if config doesn't have language
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        try {
          await this.waitForInitialization(mainWindow)
          const language = await mainWindow.webContents.executeJavaScript(
            "window.__LANGUAGE__"
          )

          if (
            typeof language === "string" &&
            language !== undefined &&
            language !== null
          ) {
            return language
          }
        } catch (err) {
          console.warn("Could not get language from window", err)
        }
      }

      // Default fallback
      return "python"
    } catch (error) {
      console.error("Error getting language:", error)
      return "python"
    }
  }

  public async processScreenshots(): Promise<void> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return

    const config = configHelper.loadConfig()

    // First verify we have a valid AI client
    if (config.apiProvider === "openai" && !this.openaiClient) {
      this.initializeAIClient()

      if (!this.openaiClient) {
        console.error("OpenAI client not initialized")
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        )
        return
      }
    } else if (config.apiProvider === "gemini" && !this.geminiApiKey) {
      this.initializeAIClient()

      if (!this.geminiApiKey) {
        console.error("Gemini API key not initialized")
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        )
        return
      }
    } else if (config.apiProvider === "anthropic" && !this.anthropicClient) {
      this.initializeAIClient()

      if (!this.anthropicClient) {
        console.error("Anthropic client not initialized")
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        )
        return
      }
    }

    // Check if we have captured images to process
    if (this.capturedImages.length === 0) {
      console.log("No captured images to process")
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS)
      return
    }

    const view = this.deps.getView()
    console.log("Processing screenshots in view:", view)

    if (view === "queue") {
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.INITIAL_START)
      
      try {
        // Initialize AbortController
        this.currentProcessingAbortController = new AbortController()
        const { signal } = this.currentProcessingAbortController

        // Convert captured images to the format expected by processing
        const screenshots = this.capturedImages.map(img => ({
          path: img.path,
          preview: img.preview,
          data: img.preview.split(',')[1] // Extract base64 data
        }))

        const result = await this.processScreenshotsHelper(screenshots, signal)

        if (!result.success) {
          console.log("Processing failed:", result.error)
          if (result.error?.includes("API Key") || result.error?.includes("OpenAI") || result.error?.includes("Gemini")) {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.API_KEY_INVALID
            )
          } else {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
              result.error
            )
          }
          this.deps.setView("queue")
          return
        }

        // Only set view to solutions if processing succeeded
        console.log("Setting view to solutions after successful processing")
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
          result.data
        )
        this.deps.setView("solutions")
      } catch (error: any) {
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
          error
        )
        console.error("Processing error:", error)
        this.deps.setView("queue")
      } finally {
        this.currentProcessingAbortController = null
      }
    }
  }

  private async processScreenshotsHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    try {
      const config = configHelper.loadConfig()
      const language = await this.getLanguage()
      const mainWindow = this.deps.getMainWindow()

      // Step 1: Extract problem info using AI Vision API
      const imageDataList = screenshots.map(screenshot => screenshot.data)

      // Update the user on progress
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Analyzing problem from screenshots...",
          progress: 20
        })
      }

      // For now, return a simple success response
      // TODO: Implement full AI processing logic here
      return {
        success: true,
        data: {
          problem: "Sample problem extracted from images",
          solution: "Sample solution generated",
          language: language
        }
      }
    } catch (error) {
      console.error("Error in processScreenshotsHelper:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Processing failed"
      }
    }
  }

  public getCapturedImages(): CapturedImage[] {
    return [...this.capturedImages]
  }

  public clearCapturedImages(): void {
    this.capturedImages = []
  }

  public async triggerScreenshotCapture(): Promise<void> {
    try {
      await this.deps.serverConnection.captureScreenshot()
    } catch (error) {
      console.error("Error triggering screenshot capture:", error)
      throw error
    }
  }

  public async triggerWebcamCapture(): Promise<void> {
    try {
      await this.deps.serverConnection.captureWebcam()
    } catch (error) {
      console.error("Error triggering webcam capture:", error)
      throw error
    }
  }
}
